<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Семейное древо - Редактор</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: #1a1a1a;
            color: white;
            overflow: hidden;
            height: 100vh;
        }

        .toolbar {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            padding: 15px;
            border-radius: 15px;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: 10px;
            min-width: 200px;
        }

        .btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.2s ease;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(79, 172, 254, 0.4);
        }

        .btn:active {
            transform: translateY(0);
        }

        .btn.female {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }

        .btn.danger {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        }

        .btn.success {
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
        }

        .canvas-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            overflow: hidden;
            cursor: grab;
        }

        .canvas-container.dragging {
            cursor: grabbing;
        }

        .canvas {
            position: absolute;
            width: 5000px;
            height: 3000px;
            background: 
                radial-gradient(circle at 20% 20%, rgba(79, 172, 254, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(250, 112, 154, 0.1) 0%, transparent 50%),
                linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transform-origin: 0 0;
            transition: transform 0.1s ease-out;
        }

        .person-card {
            position: absolute;
            width: 180px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            cursor: move;
            transition: all 0.2s ease;
            border: 2px solid transparent;
            color: #2c3e50;
            z-index: 10; /* Карточки поверх линий */
            /* Hardware acceleration оптимизации */
            will-change: transform;
            transform: translateZ(0);
            /* Убираем contain чтобы плюсики не обрезались */
            overflow: visible;
        }

        .person-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 40px rgba(0,0,0,0.4);
        }

        .person-card.selected {
            border-color: #4facfe;
            box-shadow: 0 8px 32px rgba(79, 172, 254, 0.5);
        }

        .person-card.dragging {
            transform: translateZ(0) rotate(5deg) scale(1.05);
            z-index: 1000;
            will-change: transform;
        }

        .connection-point {
            position: absolute;
            width: 20px;
            height: 20px;
            background: #4facfe;
            border: 2px solid white;
            border-radius: 50%;
            cursor: pointer;
            opacity: 0.6; /* Всегда видимы, но полупрозрачные */
            transition: all 0.2s ease;
            z-index: 100;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            font-weight: bold;
        }

        .connection-point:hover {
            background: #00f2fe;
            transform: scale(1.2);
        }

        .person-card:hover .connection-point {
            opacity: 1;
        }

        .connection-point.top {
            top: -10px;
            left: 50%;
            transform: translateX(-50%);
        }

        .connection-point.bottom {
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
        }

        .connection-point.left {
            left: -10px;
            top: 50%;
            transform: translateY(-50%);
        }

        .connection-point.right {
            right: -10px;
            top: 50%;
            transform: translateY(-50%);
        }

        .connection-point:hover {
            transform: translateX(-50%) scale(1.2);
        }

        .connection-point.top:hover {
            transform: translateX(-50%) scale(1.2);
        }

        .connection-point.bottom:hover {
            transform: translateX(-50%) scale(1.2);
        }

        .connection-point.left:hover {
            transform: translateY(-50%) scale(1.2);
        }

        .connection-point.right:hover {
            transform: translateY(-50%) scale(1.2);
        }

        .person-card.male {
            border-left: 4px solid #4facfe;
        }

        .person-card.female {
            border-left: 4px solid #fa709a;
        }

        .person-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            margin: 0 auto 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .person-icon.male {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .person-icon.female {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }

        .person-name {
            font-size: 14px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 6px;
            color: #2c3e50;
            word-wrap: break-word;
        }

        .person-dates {
            font-size: 11px;
            color: #7f8c8d;
            text-align: center;
            margin-bottom: 6px;
        }

        .person-role {
            font-size: 10px;
            color: #e74c3c;
            text-align: center;
            font-weight: bold;
            text-transform: uppercase;
        }

        .edit-form {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.5);
            z-index: 2000;
            min-width: 350px;
            display: none;
            color: #2c3e50;
        }

        .edit-form h3 {
            margin-bottom: 20px;
            text-align: center;
            color: #2c3e50;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #34495e;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 10px;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
        }

        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #4facfe;
        }

        .form-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
        }

        .overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            z-index: 1500;
            display: none;
        }

        .connection-line {
            position: absolute;
            height: 3px;
            background: linear-gradient(90deg, #4facfe, #00f2fe);
            transform-origin: left center;
            z-index: 1;
            pointer-events: all;
            border-radius: 2px;
            box-shadow: 0 2px 8px rgba(79, 172, 254, 0.3);
            cursor: pointer;
            /* Hardware acceleration для линий */
            will-change: transform;
            transform: translateZ(0);
        }

        .parent-child-connection {
            position: absolute;
            z-index: 1;
            pointer-events: none;
        }

        .connection-line:hover {
            height: 5px;
            box-shadow: 0 3px 12px rgba(79, 172, 254, 0.5);
        }

        .line-add-button {
            position: absolute;
            width: 24px;
            height: 24px;
            background: #2ecc71;
            border: 2px solid white;
            border-radius: 50%;
            cursor: pointer;
            opacity: 0;
            transition: all 0.2s ease;
            z-index: 200;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            font-weight: bold;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .connection-line:hover .line-add-button {
            opacity: 1;
        }

        .line-add-button:hover {
            background: #27ae60;
            transform: translate(-50%, -50%) scale(1.2);
        }

        .temp-line {
            position: absolute;
            height: 2px;
            background: rgba(79, 172, 254, 0.7);
            transform-origin: left center;
            z-index: 50;
            pointer-events: none;
            border-radius: 1px;
        }

        .zoom-controls {
            position: fixed;
            bottom: 20px;
            right: 20px;
            display: flex;
            flex-direction: column;
            gap: 10px;
            z-index: 1000;
        }

        .zoom-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            border: none;
            font-size: 20px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .zoom-btn:hover {
            background: rgba(79, 172, 254, 0.8);
            transform: scale(1.1);
        }

        .info-panel {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 10px;
            font-size: 12px;
            color: #bdc3c7;
            max-width: 300px;
        }

        .info-panel h4 {
            color: #4facfe;
            margin-bottom: 8px;
        }

        .info-panel div {
            margin-bottom: 4px;
        }

        .file-input {
            display: none;
        }
    </style>
</head>
<body>
    <div class="toolbar">
        <button class="btn male" onclick="addPerson('male')">👨 Мужчина</button>
        <button class="btn female" onclick="addPerson('female')">👩 Женщина</button>
        <hr style="border: 1px solid rgba(255,255,255,0.2); margin: 5px 0;">
        <button class="btn success" onclick="saveData()">💾 Сохранить</button>
        <button class="btn" onclick="loadData()">📁 Загрузить</button>
        <hr style="border: 1px solid rgba(255,255,255,0.2); margin: 5px 0;">
        <button class="btn" onclick="clearAutoSave()" title="Очистить автосохранение">🧹 Очистить авто</button>
        <button class="btn danger" onclick="clearAll()">🗑️ Очистить все</button>
        <input type="file" id="fileInput" class="file-input" accept=".json" onchange="handleFileLoad(event)">
    </div>

    <div class="canvas-container" id="canvasContainer">
        <div class="canvas" id="canvas"></div>
    </div>

    <div class="zoom-controls">
        <button class="zoom-btn" onclick="zoomIn()">+</button>
        <button class="zoom-btn" onclick="zoomOut()">−</button>
        <button class="zoom-btn" onclick="resetZoom()">⌂</button>
    </div>

    <div class="info-panel">
        <h4>Управление:</h4>
        <div>• Двойной клик - редактировать</div>
        <div>• Перетаскивание - перемещать</div>
        <div>• Колесо мыши - зум</div>
        <div>• Средняя кнопка - панорама</div>
        <div>• Наведи на карточку → клик плюсик</div>
        <div>• Плюс на линии = создать ребенка</div>
        <div id="autoSaveStatus" style="margin-top: 10px; padding-top: 8px; border-top: 1px solid rgba(255,255,255,0.2); font-size: 11px; color: #2ecc71;">
            💾 Автосохранение включено
        </div>
    </div>

    <div class="overlay" id="overlay" onclick="closeEditForm()"></div>
    
    <div class="edit-form" id="editForm">
        <h3>Редактировать персону</h3>
        <div class="form-group">
            <label>Имя:</label>
            <input type="text" id="editName" placeholder="Введите полное имя">
        </div>
        <div class="form-group">
            <label>Даты жизни:</label>
            <input type="text" id="editDates" placeholder="например: 1950-2020 или 1950">
        </div>
        <div class="form-group">
            <label>Роль в семье:</label>
            <input type="text" id="editRole" placeholder="отец, мать, сын, дочь, дедушка...">
        </div>
        <div class="form-group">
            <label>Пол:</label>
            <select id="editGender">
                <option value="male">Мужской</option>
                <option value="female">Женский</option>
            </select>
        </div>
        <div class="form-buttons">
            <button class="btn success" onclick="saveEdit()">Сохранить</button>
            <button class="btn danger" onclick="deleteCard()">Удалить</button>
            <button class="btn" onclick="closeEditForm()">Отмена</button>
        </div>
    </div>

    <script>
        let people = [];
        let connections = [];
        let draggedElement = null;
        let dragOffset = { x: 0, y: 0 };
        let connectionMode = false;
        let selectedCard = null;
        let editingCard = null;
        let nextId = 1;

        // Кэши для оптимизации производительности
        let connectionCache = new Map(); // ID связи -> DOM элемент линии
        let cardElementCache = new Map(); // ID карточки -> DOM элемент
        let cardPositionCache = new Map(); // ID карточки -> {x, y}
        let dirtyConnections = new Set(); // ID связей, которые нужно обновить
        let isUpdateScheduled = false; // Флаг для предотвращения множественных обновлений
        let memoizedCalculations = new Map(); // Кэш для мемоизации вычислений

        // Зум и панорама
        let scale = 1;
        let panX = 0;
        let panY = 0;
        let isPanning = false;
        let panStart = { x: 0, y: 0 };
        let panOffset = { x: 0, y: 0 };

        // Автосохранение
        let autoSaveTimeout = null;
        const autoSaveKey = 'family_tree_autosave';
        const autoSaveDelay = 2000; // 2 секунды задержки

        const canvas = document.getElementById('canvas');
        const canvasContainer = document.getElementById('canvasContainer');

        // Инициализация
        function init() {
            initializeCaches();
            setupEventListeners();
            panX = (window.innerWidth - 5000 * scale) / 2;
            panY = (window.innerHeight - 3000 * scale) / 2;
            updateCanvasTransform();
            autoLoad(); // Загружаем автосохранение при запуске
        }

        function initializeCaches() {
            connectionCache.clear();
            cardElementCache.clear();
            cardPositionCache.clear();
            dirtyConnections.clear();
            memoizedCalculations.clear();
            isUpdateScheduled = false;
        }

        function setupEventListeners() {
            canvasContainer.addEventListener('wheel', handleWheel, { passive: false });
            canvasContainer.addEventListener('mousedown', handlePanStart);
            document.addEventListener('mousemove', handlePanMove);
            document.addEventListener('mouseup', handlePanEnd);
            canvas.addEventListener('click', handleCanvasClick);
            canvasContainer.addEventListener('contextmenu', e => e.preventDefault());
        }

        function handleWheel(e) {
            e.preventDefault();

            const rect = canvasContainer.getBoundingClientRect();
            const mouseX = e.clientX - rect.left;
            const mouseY = e.clientY - rect.top;

            const oldScale = scale;
            const zoomFactor = e.deltaY > 0 ? 0.9 : 1.1;
            scale = Math.max(0.1, Math.min(3, scale * zoomFactor));

            const scaleChange = scale / oldScale;
            panX = mouseX - (mouseX - panX) * scaleChange;
            panY = mouseY - (mouseY - panY) * scaleChange;

            updateCanvasTransform();
        }

        function handlePanStart(e) {
            // Средняя кнопка мыши или левая кнопка НЕ на карточке
            const clickedCard = e.target.closest('.person-card');
            const clickedConnectionPoint = e.target.closest('.connection-point');

            if (e.button === 1 || (e.button === 0 && !clickedCard && !clickedConnectionPoint)) {
                isPanning = true;
                panStart.x = e.clientX;
                panStart.y = e.clientY;
                panOffset.x = panX;
                panOffset.y = panY;
                canvasContainer.classList.add('dragging');
                e.preventDefault();
            }
        }

        function handlePanMove(e) {
            if (isPanning) {
                panX = panOffset.x + (e.clientX - panStart.x);
                panY = panOffset.y + (e.clientY - panStart.y);
                updateCanvasTransform();
            }
        }

        function handlePanEnd(e) {
            if (isPanning) {
                isPanning = false;
                canvasContainer.classList.remove('dragging');
            }
        }

        function updateCanvasTransform() {
            canvas.style.transform = `translate(${panX}px, ${panY}px) scale(${scale})`;
            markAllConnectionsDirty();
            scheduleConnectionUpdate();
        }

        // Функции автосохранения
        function autoSave() {
            try {
                const data = {
                    people: people,
                    connections: connections,
                    nextId: nextId,
                    timestamp: Date.now()
                };

                localStorage.setItem(autoSaveKey, JSON.stringify(data));
                updateAutoSaveStatus('💾 Сохранено автоматически', '#2ecc71');

                // Через 3 секунды возвращаем обычный статус
                setTimeout(() => {
                    updateAutoSaveStatus('💾 Автосохранение включено', '#2ecc71');
                }, 3000);

            } catch (error) {
                console.error('Ошибка автосохранения:', error);
                updateAutoSaveStatus('⚠️ Ошибка автосохранения', '#e74c3c');
            }
        }

        function scheduleAutoSave() {
            // Отменяем предыдущий таймер
            if (autoSaveTimeout) {
                clearTimeout(autoSaveTimeout);
            }

            updateAutoSaveStatus('💾 Сохранение...', '#f39c12');

            // Устанавливаем новый таймер
            autoSaveTimeout = setTimeout(() => {
                autoSave();
                autoSaveTimeout = null;
            }, autoSaveDelay);
        }

        function autoLoad() {
            try {
                const savedData = localStorage.getItem(autoSaveKey);
                if (savedData) {
                    const data = JSON.parse(savedData);
                    const savedDate = new Date(data.timestamp);

                    if (confirm(`Найдено автосохранение от ${savedDate.toLocaleString()}.\nВосстановить данные?`)) {
                        // Очищаем текущие данные
                        canvas.innerHTML = '';
                        initializeCaches();

                        // Загружаем сохраненные данные
                        people = data.people || [];
                        connections = data.connections || [];
                        nextId = data.nextId || 1;

                        // Создаем карточки
                        people.forEach(person => {
                            const card = createPersonCard(person);
                            canvas.appendChild(card);
                        });

                        // Обновляем связи
                        markAllConnectionsDirty();
                        scheduleConnectionUpdate();

                        updateAutoSaveStatus('💾 Данные восстановлены', '#2ecc71');
                    }
                }
            } catch (error) {
                console.error('Ошибка загрузки автосохранения:', error);
                updateAutoSaveStatus('⚠️ Ошибка загрузки', '#e74c3c');
            }
        }

        function clearAutoSave() {
            try {
                localStorage.removeItem(autoSaveKey);
                updateAutoSaveStatus('🧹 Автосохранение очищено', '#95a5a6');
                setTimeout(() => {
                    updateAutoSaveStatus('💾 Автосохранение включено', '#2ecc71');
                }, 3000);
            } catch (error) {
                console.error('Ошибка очистки автосохранения:', error);
            }
        }

        function updateAutoSaveStatus(text, color) {
            const statusElement = document.getElementById('autoSaveStatus');
            if (statusElement) {
                statusElement.textContent = text;
                statusElement.style.color = color;
            }
        }

        function markAllConnectionsDirty() {
            connections.forEach(conn => dirtyConnections.add(conn.id));
        }

        function markConnectionDirty(connectionId) {
            dirtyConnections.add(connectionId);
        }

        function scheduleConnectionUpdate() {
            if (!isUpdateScheduled) {
                isUpdateScheduled = true;
                requestAnimationFrame(() => {
                    updateDirtyConnections();
                    isUpdateScheduled = false;
                });
            }
        }

        function zoomIn() {
            const centerX = window.innerWidth / 2;
            const centerY = window.innerHeight / 2;

            const oldScale = scale;
            scale = Math.min(3, scale * 1.2);

            const scaleChange = scale / oldScale;
            panX = centerX - (centerX - panX) * scaleChange;
            panY = centerY - (centerY - panY) * scaleChange;

            updateCanvasTransform();
        }

        function zoomOut() {
            const centerX = window.innerWidth / 2;
            const centerY = window.innerHeight / 2;

            const oldScale = scale;
            scale = Math.max(0.1, scale * 0.8);

            const scaleChange = scale / oldScale;
            panX = centerX - (centerX - panX) * scaleChange;
            panY = centerY - (centerY - panY) * scaleChange;

            updateCanvasTransform();
        }

        function resetZoom() {
            scale = 1;
            panX = (window.innerWidth - 5000) / 2;
            panY = (window.innerHeight - 3000) / 2;
            updateCanvasTransform();
        }

        function addPerson(gender) {
            const centerX = (-panX + window.innerWidth / 2) / scale;
            const centerY = (-panY + window.innerHeight / 2) / scale;

            const person = {
                id: nextId++,
                name: 'Новый человек',
                dates: '',
                role: '',
                gender: gender,
                x: centerX - 90 + Math.random() * 180,
                y: centerY - 75 + Math.random() * 150
            };

            people.push(person);
            const card = createPersonCard(person);
            canvas.appendChild(card);

            // Автосохранение
            scheduleAutoSave();
        }

        function createPersonCard(data) {
            const card = document.createElement('div');
            card.className = `person-card ${data.gender}`;
            card.style.left = data.x + 'px';
            card.style.top = data.y + 'px';
            card.dataset.id = data.id;

            const icon = data.gender === 'male' ? '👨' : '👩';

            card.innerHTML = `
                <div class="person-icon ${data.gender}">${icon}</div>
                <div class="person-name">${data.name}</div>
                <div class="person-dates">${data.dates}</div>
                <div class="person-role">${data.role}</div>
                <div class="connection-point top" data-direction="top">+</div>
                <div class="connection-point bottom" data-direction="bottom">+</div>
                <div class="connection-point left" data-direction="left">+</div>
                <div class="connection-point right" data-direction="right">+</div>
            `;

            // События для карточки
            card.addEventListener('mousedown', startDrag);
            card.addEventListener('dblclick', (e) => {
                e.stopPropagation();
                editCard(data.id);
            });
            card.addEventListener('click', (e) => {
                e.stopPropagation();
                selectCard(data.id);
            });

            // События для точек подключения
            const connectionPoints = card.querySelectorAll('.connection-point');
            connectionPoints.forEach(point => {
                point.addEventListener('click', (e) => {
                    e.stopPropagation();
                    createConnectedPerson(data.id, point.dataset.direction);
                });
            });

            // Добавляем в кэш и кэшируем позицию
            cardElementCache.set(data.id, card);
            cardPositionCache.set(data.id, { x: data.x, y: data.y });

            return card;
        }

        function startDrag(e) {
            // Проверяем, что это не клик на плюсик
            if (e.target.closest('.connection-point')) return;

            if (connectionMode || isPanning) return;

            draggedElement = e.target.closest('.person-card');

            // Получаем текущую позицию карточки в координатах canvas
            const currentX = parseFloat(draggedElement.style.left);
            const currentY = parseFloat(draggedElement.style.top);

            // Преобразуем координаты мыши в координаты canvas
            const mouseCanvasX = (e.clientX - panX) / scale;
            const mouseCanvasY = (e.clientY - panY) / scale;

            // Вычисляем смещение от левого верхнего угла карточки
            dragOffset.x = mouseCanvasX - currentX;
            dragOffset.y = mouseCanvasY - currentY;

            document.addEventListener('mousemove', drag);
            document.addEventListener('mouseup', stopDrag);

            draggedElement.classList.add('dragging');
            e.preventDefault();
            e.stopPropagation(); // Останавливаем всплытие, чтобы не запустилась панорама
        }

        function drag(e) {
            if (!draggedElement) return;

            // Преобразуем координаты мыши в координаты canvas
            const mouseCanvasX = (e.clientX - panX) / scale;
            const mouseCanvasY = (e.clientY - panY) / scale;

            // Вычисляем новую позицию карточки
            let x = mouseCanvasX - dragOffset.x;
            let y = mouseCanvasY - dragOffset.y;

            // Ограничиваем по границам canvas
            x = Math.max(0, Math.min(x, 5000 - 180));
            y = Math.max(0, Math.min(y, 3000 - 200));

            // Устанавливаем позицию
            draggedElement.style.left = x + 'px';
            draggedElement.style.top = y + 'px';

            // Обновляем данные и кэш позиций
            const id = parseInt(draggedElement.dataset.id);
            const person = people.find(p => p.id === id);
            if (person) {
                person.x = x;
                person.y = y;
                cardPositionCache.set(id, { x, y });

                // Помечаем связи этой карточки как грязные
                connections.forEach(conn => {
                    if (conn.from === id || conn.to === id ||
                        (conn.type === 'parent-child' &&
                         (conn.child === id || conn.parents.includes(id)))) {
                        markConnectionDirty(conn.id);
                    }
                });
            }

            // Оптимизированное обновление через requestAnimationFrame
            scheduleConnectionUpdate();
        }

        function stopDrag() {
            if (draggedElement) {
                draggedElement.classList.remove('dragging');
                draggedElement = null;
            }
            document.removeEventListener('mousemove', drag);
            document.removeEventListener('mouseup', stopDrag);

            // Гарантированно обновляем связи в конце перетаскивания
            scheduleConnectionUpdate();

            // Автосохранение после завершения перетаскивания
            scheduleAutoSave();
        }

        function selectCard(id) {
            document.querySelectorAll('.person-card').forEach(card => {
                card.classList.remove('selected');
            });

            const card = document.querySelector(`[data-id="${id}"]`);
            if (card) {
                card.classList.add('selected');
                selectedCard = id;
            }
        }

        function editCard(id) {
            const person = people.find(p => p.id === id);
            if (!person) return;

            editingCard = id;

            document.getElementById('editName').value = person.name;
            document.getElementById('editDates').value = person.dates;
            document.getElementById('editRole').value = person.role;
            document.getElementById('editGender').value = person.gender;

            document.getElementById('overlay').style.display = 'block';
            document.getElementById('editForm').style.display = 'block';
        }

        function saveEdit() {
            if (!editingCard) return;

            const person = people.find(p => p.id === editingCard);
            if (!person) return;

            person.name = document.getElementById('editName').value || 'Без имени';
            person.dates = document.getElementById('editDates').value;
            person.role = document.getElementById('editRole').value;
            person.gender = document.getElementById('editGender').value;

            const card = document.querySelector(`[data-id="${editingCard}"]`);
            if (card) {
                card.className = `person-card ${person.gender}`;
                const icon = person.gender === 'male' ? '👨' : '👩';
                card.innerHTML = `
                    <div class="person-icon ${person.gender}">${icon}</div>
                    <div class="person-name">${person.name}</div>
                    <div class="person-dates">${person.dates}</div>
                    <div class="person-role">${person.role}</div>
                    <div class="connection-point top" data-direction="top">+</div>
                    <div class="connection-point bottom" data-direction="bottom">+</div>
                    <div class="connection-point left" data-direction="left">+</div>
                    <div class="connection-point right" data-direction="right">+</div>
                `;

                card.addEventListener('mousedown', startDrag);
                card.addEventListener('dblclick', (e) => {
                    e.stopPropagation();
                    editCard(person.id);
                });
                card.addEventListener('click', (e) => {
                    e.stopPropagation();
                    if (connectionMode) {
                        handleConnection(person.id);
                    } else {
                        selectCard(person.id);
                    }
                });

                // Добавляем события для плюсиков после перезаписи HTML
                const connectionPoints = card.querySelectorAll('.connection-point');
                connectionPoints.forEach(point => {
                    point.addEventListener('click', (e) => {
                        e.stopPropagation();
                        createConnectedPerson(person.id, point.dataset.direction);
                    });
                });

                // Обновляем кэши
                cardElementCache.set(person.id, card);
                cardPositionCache.set(person.id, { x: person.x, y: person.y });
            }

            closeEditForm();

            // Автосохранение
            scheduleAutoSave();
        }

        function deleteCard() {
            if (!editingCard) return;

            people = people.filter(p => p.id !== editingCard);

            // Помечаем связи для удаления и удаляем их
            const connectionsToRemove = connections.filter(c =>
                c.from === editingCard || c.to === editingCard ||
                (c.type === 'parent-child' && (c.child === editingCard || c.parents.includes(editingCard)))
            );

            connectionsToRemove.forEach(conn => {
                markConnectionDirty(conn.id);
            });

            // Удаляем обычные связи
            connections = connections.filter(c => c.from !== editingCard && c.to !== editingCard);

            // Удаляем связи parent-child где удаляемый человек является ребенком или родителем
            connections = connections.filter(c => {
                if (c.type === 'parent-child') {
                    return c.child !== editingCard && !c.parents.includes(editingCard);
                }
                return true;
            });

            // Удаляем из кэшей
            const card = cardElementCache.get(editingCard);
            if (card) {
                card.remove();
            }
            cardElementCache.delete(editingCard);
            cardPositionCache.delete(editingCard);

            scheduleConnectionUpdate();
            closeEditForm();

            // Автосохранение
            scheduleAutoSave();
        }

        function closeEditForm() {
            document.getElementById('overlay').style.display = 'none';
            document.getElementById('editForm').style.display = 'none';
            editingCard = null;
        }

        // Простая система: клик на плюсик = создать карточку
        function createConnectedPerson(parentId, direction) {
            const parentCard = document.querySelector(`[data-id="${parentId}"]`);
            const parentX = parseFloat(parentCard.style.left);
            const parentY = parseFloat(parentCard.style.top);

            // Вычисляем позицию новой карточки в зависимости от направления
            let newX, newY;
            const spacing = 250; // Расстояние между карточками

            switch (direction) {
                case 'top':
                    newX = parentX;
                    newY = parentY - spacing;
                    break;
                case 'bottom':
                    newX = parentX;
                    newY = parentY + spacing;
                    break;
                case 'left':
                    newX = parentX - spacing;
                    newY = parentY;
                    break;
                case 'right':
                    newX = parentX + spacing;
                    newY = parentY;
                    break;
            }

            // Ограничиваем по границам canvas
            newX = Math.max(0, Math.min(newX, 5000 - 180));
            newY = Math.max(0, Math.min(newY, 3000 - 200));

            // Создаем нового человека
            const newPerson = {
                id: nextId++,
                name: 'Новый человек',
                dates: '',
                role: '',
                gender: 'male', // По умолчанию
                x: newX,
                y: newY
            };

            people.push(newPerson);
            const card = createPersonCard(newPerson);
            canvas.appendChild(card);

            // Создаем связь между родителем и новым человеком
            const oppositeDirection = getOppositeDirection(direction);
            const connection = {
                id: Date.now(),
                from: parentId,
                fromDirection: direction,
                to: newPerson.id,
                toDirection: oppositeDirection
            };

            connections.push(connection);
            markConnectionDirty(connection.id);
            scheduleConnectionUpdate();

            // Автосохранение
            scheduleAutoSave();

            // Автоматически открываем редактор для нового человека
            setTimeout(() => editCard(newPerson.id), 100);
        }

        function getOppositeDirection(direction) {
            const opposites = {
                'top': 'bottom',
                'bottom': 'top',
                'left': 'right',
                'right': 'left'
            };
            return opposites[direction];
        }

        function getConnectionPoint(card, direction) {
            const x = parseFloat(card.style.left);
            const y = parseFloat(card.style.top);
            const width = 180;
            const height = 200;

            switch (direction) {
                case 'top': return { x: x + width/2, y: y + height/2 }; // Из центра карточки
                case 'bottom': return { x: x + width/2, y: y + height/2 }; // Из центра карточки
                case 'left': return { x: x + width/2, y: y + height/2 }; // Из центра карточки
                case 'right': return { x: x + width/2, y: y + height/2 }; // Из центра карточки
                default: return { x: x + width/2, y: y + height/2 };
            }
        }

        function getConnectionPointCached(card, direction) {
            const cardId = parseInt(card.dataset.id);
            const cachedPos = cardPositionCache.get(cardId);

            if (cachedPos) {
                const width = 180;
                const height = 200;
                return { x: cachedPos.x + width/2, y: cachedPos.y + height/2 };
            }

            // Fallback к обычной функции если нет в кэше
            return getConnectionPoint(card, direction);
        }

        function toggleConnectionMode() {
            // Убираем старый режим связей
            connectionMode = false;
            const btn = document.getElementById('connectionBtn');
            btn.textContent = '🔗 Новый режим связей';
            btn.style.opacity = '0.5';
            btn.onclick = null;
        }

        function updateConnections() {
            // Старая функция - теперь помечаем все связи как грязные и обновляем
            markAllConnectionsDirty();
            updateDirtyConnections();
        }

        function updateDirtyConnections() {
            // Обновляем только грязные связи
            dirtyConnections.forEach(connectionId => {
                const conn = connections.find(c => c.id === connectionId);
                if (!conn) {
                    // Связь была удалена - удаляем из кэша
                    const cachedLine = connectionCache.get(connectionId);
                    if (cachedLine) {
                        cachedLine.remove();
                        connectionCache.delete(connectionId);
                    }
                    return;
                }

                // Удаляем старую линию если есть
                const existingLine = connectionCache.get(connectionId);
                if (existingLine) {
                    existingLine.remove();
                }

                // Создаем новую линию
                if (conn.type === 'parent-child') {
                    drawParentChildConnection(conn);
                } else {
                    const fromCard = cardElementCache.get(conn.from);
                    const toCard = cardElementCache.get(conn.to);

                    if (fromCard && toCard) {
                        drawConnection(conn, fromCard, toCard);
                    }
                }
            });

            // Очищаем список грязных связей
            dirtyConnections.clear();
        }

        function drawConnection(connectionData, fromCard, toCard) {
            // Получаем точки подключения с мемоизацией
            const fromPoint = getConnectionPointCached(fromCard, connectionData.fromDirection);
            const toPoint = getConnectionPointCached(toCard, connectionData.toDirection);

            const line = document.createElement('div');
            line.className = 'connection-line';
            line.dataset.connectionId = connectionData.id;

            // Мемоизация вычислений длины и угла
            const cacheKey = `${fromPoint.x},${fromPoint.y}-${toPoint.x},${toPoint.y}`;
            let calculations = memoizedCalculations.get(cacheKey);

            if (!calculations) {
                const length = Math.sqrt(Math.pow(toPoint.x - fromPoint.x, 2) + Math.pow(toPoint.y - fromPoint.y, 2));
                const angle = Math.atan2(toPoint.y - fromPoint.y, toPoint.x - fromPoint.x) * 180 / Math.PI;
                calculations = { length, angle };
                memoizedCalculations.set(cacheKey, calculations);
            }

            line.style.width = calculations.length + 'px';
            line.style.left = fromPoint.x + 'px';
            line.style.top = fromPoint.y + 'px';
            line.style.transform = `rotate(${calculations.angle}deg)`;

            // Добавляем кнопку для создания ребенка
            const addButton = document.createElement('div');
            addButton.className = 'line-add-button';
            addButton.textContent = '+';
            addButton.onclick = (e) => {
                e.stopPropagation();
                createChild(connectionData, fromPoint, toPoint);
            };

            line.appendChild(addButton);
            canvas.appendChild(line);

            // Добавляем в кэш
            connectionCache.set(connectionData.id, line);
        }

        function drawParentChildConnection(connection) {
            const parent1Card = cardElementCache.get(connection.parents[0]);
            const parent2Card = cardElementCache.get(connection.parents[1]);
            const childCard = cardElementCache.get(connection.child);

            if (!parent1Card || !parent2Card || !childCard) return;

            // Получаем позиции из кэша для оптимизации
            const parent1Pos = cardPositionCache.get(connection.parents[0]);
            const parent2Pos = cardPositionCache.get(connection.parents[1]);
            const childPos = cardPositionCache.get(connection.child);

            if (!parent1Pos || !parent2Pos || !childPos) return;

            const parent1X = parent1Pos.x + 90; // центр карточки
            const parent1Y = parent1Pos.y + 100; // ЦЕНТР карточки по вертикали
            const parent2X = parent2Pos.x + 90;
            const parent2Y = parent2Pos.y + 100; // ЦЕНТР карточки по вертикали
            const childX = childPos.x + 90; // центр карточки
            const childY = childPos.y; // верх карточки

            // Центр между родителями (теперь на уровне их центров)
            const centerX = (parent1X + parent2X) / 2;
            const centerY = Math.max(parent1Y, parent2Y);

            // Находим всех детей этой пары
            const allChildren = connections.filter(conn =>
                conn.type === 'parent-child' &&
                conn.parents.includes(connection.parents[0]) &&
                conn.parents.includes(connection.parents[1])
            );

            // Создаем контейнер для всех линий этой связи
            const connectionContainer = document.createElement('div');
            connectionContainer.className = 'parent-child-connection';
            connectionContainer.dataset.connectionId = connection.id;

            if (allChildren.length === 1) {
                // Один ребенок - используем ту же систему разветвления что и для нескольких детей
                const branchY = centerY + (childY - centerY) / 2;

                // Вертикальная линия вниз от центра родителей до горизонтальной линии
                connectionContainer.appendChild(createStraightLineElement(centerX, centerY, centerX, branchY));

                // Горизонтальная линия от центра к ребенку
                connectionContainer.appendChild(createStraightLineElement(centerX, branchY, childX, branchY));

                // Вертикальная линия к ребенку
                connectionContainer.appendChild(createStraightLineElement(childX, branchY, childX, childY));
            } else {
                // Несколько детей - система разветвления

                // Находим самого высокого ребенка (с минимальным Y) используя кэш
                const childYPositions = allChildren.map(conn => {
                    const pos = cardPositionCache.get(conn.child);
                    return pos ? pos.y : 0;
                });
                const highestChildY = Math.min(...childYPositions);

                // Горизонтальная линия на половине пути между родителями и детьми
                const branchY = centerY + (highestChildY - centerY) / 2;

                // Вертикальная линия вниз от центра родителей до горизонтальной линии
                connectionContainer.appendChild(createStraightLineElement(centerX, centerY, centerX, branchY));

                // Горизонтальная линия между детьми используя кэш
                const childPositions = allChildren.map(conn => {
                    const pos = cardPositionCache.get(conn.child);
                    return pos ? pos.x + 90 : 0;
                }).sort((a, b) => a - b);

                const leftmostX = Math.min(...childPositions);
                const rightmostX = Math.max(...childPositions);
                connectionContainer.appendChild(createStraightLineElement(leftmostX, branchY, rightmostX, branchY));

                // Вертикальные линии к каждому ребенку используя кэш
                allChildren.forEach(conn => {
                    const pos = cardPositionCache.get(conn.child);
                    if (pos) {
                        const cX = pos.x + 90;
                        const cY = pos.y; // верх карточки ребенка
                        connectionContainer.appendChild(createStraightLineElement(cX, branchY, cX, cY));
                    }
                });
            }

            canvas.appendChild(connectionContainer);
            connectionCache.set(connection.id, connectionContainer);
        }

        function createStraightLineElement(x1, y1, x2, y2) {
            const line = document.createElement('div');
            line.className = 'connection-line';

            if (x1 === x2) {
                // Вертикальная линия
                line.style.width = '3px';
                line.style.height = Math.abs(y2 - y1) + 'px';
                line.style.left = x1 - 1.5 + 'px';
                line.style.top = Math.min(y1, y2) + 'px';
                line.style.transform = 'none';
            } else {
                // Горизонтальная линия
                line.style.width = Math.abs(x2 - x1) + 'px';
                line.style.height = '3px';
                line.style.left = Math.min(x1, x2) + 'px';
                line.style.top = y1 - 1.5 + 'px';
                line.style.transform = 'none';
            }

            return line;
        }

        function drawStraightLine(x1, y1, x2, y2) {
            const line = createStraightLineElement(x1, y1, x2, y2);
            canvas.appendChild(line);
        }

        function createChild(parentConnection, fromPoint, toPoint) {
            // Находим существующих детей этой пары
            const existingChildren = connections.filter(conn =>
                (conn.from == parentConnection.from || conn.from == parentConnection.to) &&
                conn.fromDirection === 'bottom'
            );

            // Вычисляем позицию для нового ребенка
            const centerX = (fromPoint.x + toPoint.x) / 2;
            const baseY = Math.max(fromPoint.y, toPoint.y) + 200; // Ниже родителей

            // Если уже есть дети, размещаем нового справа от последнего
            let childX = centerX - 90;
            if (existingChildren.length > 0) {
                childX = centerX - 90 + (existingChildren.length * 220); // Сдвигаем вправо
            }

            // Создаем нового ребенка
            const child = {
                id: nextId++,
                name: 'Новый человек',
                dates: '',
                role: '', // Убираем роль "ребенок"
                gender: 'male',
                x: childX,
                y: baseY
            };

            people.push(child);
            const card = createPersonCard(child);
            canvas.appendChild(card);

            // Создаем специальную связь "родители-дети"
            const parentChildConnection = {
                id: Date.now(),
                type: 'parent-child',
                parents: [parentConnection.from, parentConnection.to],
                child: child.id
            };

            connections.push(parentChildConnection);
            markConnectionDirty(parentChildConnection.id);
            scheduleConnectionUpdate();

            // Автосохранение
            scheduleAutoSave();

            // Автоматически открываем редактор
            setTimeout(() => editCard(child.id), 100);
        }

        function clearAll() {
            if (confirm('Удалить все карточки и связи?')) {
                people = [];
                connections = [];
                canvas.innerHTML = '';
                nextId = 1;
                // Очищаем все кэши
                initializeCaches();
                // Очищаем автосохранение
                clearAutoSave();
            }
        }

        function saveData() {
            const data = {
                people: people,
                connections: connections,
                nextId: nextId
            };

            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = 'семейное_древо.json';
            a.click();

            URL.revokeObjectURL(url);
        }

        function loadData() {
            document.getElementById('fileInput').click();
        }

        function handleFileLoad(event) {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const data = JSON.parse(e.target.result);

                    canvas.innerHTML = '';

                    people = data.people || [];
                    connections = data.connections || [];
                    nextId = data.nextId || 1;

                    people.forEach(person => {
                        const card = createPersonCard(person);
                        canvas.appendChild(card);
                    });

                    markAllConnectionsDirty();
                    scheduleConnectionUpdate();

                } catch (error) {
                    alert('Ошибка при загрузке файла: ' + error.message);
                }
            };
            reader.readAsText(file);
        }

        function handleCanvasClick() {
            if (!connectionMode) {
                selectedCard = null;
                document.querySelectorAll('.person-card').forEach(card => {
                    card.classList.remove('selected');
                });
            }
        }

        // Запуск
        window.addEventListener('load', init);
    </script>
</body>
</html>
